"""
The Oracle - Enhanced Data Collection Service with Modern Architecture
=====================================================================

Enhanced Oracle service featuring:
- Dependency injection for better testability
- Async database operations for performance
- Secure configuration management
- Comprehensive error handling and monitoring
- Interface-based design for modularity
"""

import os
import logging
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass

# Add common path for shared utilities
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / 'common'))

from dependency_injection import (
    ServiceContainer, injectable, get_container,
    IDataSource, INotificationService
)
from async_database import AsyncDatabasePool, initialize_async_database, cleanup_async_database
from secure_config import get_secure_config
from error_handling import ChimeraError, log_error_with_context, retry_with_backoff
from security import InputValidator, audit_logger
from models import UnlockEvent

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


@dataclass
class DataSourceMetrics:
    """Metrics for data source operations"""
    events_fetched: int = 0
    events_stored: int = 0
    events_published: int = 0
    errors_encountered: int = 0
    last_fetch_time: Optional[datetime] = None
    fetch_duration_seconds: float = 0.0


@injectable(IDataSource)
class EnhancedTokenUnlocksDataSource:
    """Enhanced data source with async operations and error handling"""
    
    def __init__(self):
        self.config = get_secure_config()
        self.metrics = DataSourceMetrics()
        self.api_endpoints = {
            'tokenunlocks': 'https://api.tokenunlocks.com/v1/unlocks',
            'vestlab': 'https://api.vestlab.io/v1/unlocks'
        }
    
    @retry_with_backoff(max_retries=3, base_delay=2.0)
    async def fetch_unlock_events(self) -> List[Dict[str, Any]]:
        """Fetch unlock events from multiple sources with retry logic"""
        start_time = datetime.now(timezone.utc)
        all_events = []
        
        try:
            logging.info("🔍 Fetching unlock events from external sources...")
            
            # In a real implementation, this would make actual API calls
            # For now, we'll simulate the data fetching
            mock_events = await self._fetch_mock_data()
            all_events.extend(mock_events)
            
            # Update metrics
            self.metrics.events_fetched = len(all_events)
            self.metrics.last_fetch_time = start_time
            self.metrics.fetch_duration_seconds = (
                datetime.now(timezone.utc) - start_time
            ).total_seconds()
            
            logging.info(f"✅ Fetched {len(all_events)} events in {self.metrics.fetch_duration_seconds:.2f}s")
            
            # Audit log the data fetch
            audit_logger.log_security_event(
                "DATA_FETCH_COMPLETED",
                {
                    "events_count": len(all_events),
                    "duration_seconds": self.metrics.fetch_duration_seconds,
                    "sources": list(self.api_endpoints.keys())
                }
            )
            
            return all_events
            
        except Exception as e:
            self.metrics.errors_encountered += 1
            log_error_with_context(
                e,
                context={
                    'operation': 'fetch_unlock_events',
                    'sources': list(self.api_endpoints.keys()),
                    'attempt_time': start_time.isoformat()
                }
            )
            raise ChimeraError(f"Failed to fetch unlock events: {str(e)}")
    
    async def _fetch_mock_data(self) -> List[Dict[str, Any]]:
        """Mock data fetching for demonstration"""
        # Simulate API delay
        await asyncio.sleep(0.1)
        
        # Generate mock unlock events
        mock_events = [
            {
                'token_symbol': 'ETH',
                'contract_address': '0x' + '0' * 40,
                'unlock_date': (datetime.now(timezone.utc) + timedelta(days=7)).isoformat(),
                'unlock_amount': '1000000.0',
                'circulating_supply': '120000000.0',
                'total_supply': '120000000.0',
                'source': 'tokenunlocks'
            },
            {
                'token_symbol': 'MATIC',
                'contract_address': '0x' + '1' * 40,
                'unlock_date': (datetime.now(timezone.utc) + timedelta(days=10)).isoformat(),
                'unlock_amount': '5000000.0',
                'circulating_supply': '9000000000.0',
                'total_supply': '10000000000.0',
                'source': 'vestlab'
            }
        ]
        
        return mock_events
    
    async def validate_data(self, data: Dict[str, Any]) -> bool:
        """Validate unlock event data"""
        try:
            # Validate required fields
            required_fields = [
                'token_symbol', 'contract_address', 'unlock_date', 
                'unlock_amount', 'circulating_supply'
            ]
            
            for field in required_fields:
                if field not in data:
                    logging.warning(f"❌ Missing required field: {field}")
                    return False
            
            # Validate token symbol
            InputValidator.validate_token_symbol(data['token_symbol'])
            
            # Validate contract address
            InputValidator.validate_ethereum_address(data['contract_address'])
            
            # Validate amounts
            InputValidator.validate_decimal_amount(data['unlock_amount'])
            InputValidator.validate_decimal_amount(data['circulating_supply'])
            
            return True
            
        except Exception as e:
            logging.warning(f"❌ Data validation failed: {e}")
            return False
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get data source metrics"""
        return {
            'events_fetched': self.metrics.events_fetched,
            'events_stored': self.metrics.events_stored,
            'events_published': self.metrics.events_published,
            'errors_encountered': self.metrics.errors_encountered,
            'last_fetch_time': self.metrics.last_fetch_time.isoformat() if self.metrics.last_fetch_time else None,
            'fetch_duration_seconds': self.metrics.fetch_duration_seconds
        }


@injectable(INotificationService)
class EnhancedOracleNotificationService:
    """Enhanced notification service for Oracle operations"""
    
    def __init__(self):
        self.config = get_secure_config()
        self.notifications_sent = 0
    
    async def send_notification(self, message: str, priority: int = 2) -> bool:
        """Send notification about Oracle operations"""
        try:
            self.notifications_sent += 1
            
            # Log notification
            logging.info(f"📢 ORACLE NOTIFICATION [P{priority}]: {message}")
            
            # In a real implementation, this would send to Telegram, Slack, etc.
            if priority >= 3:
                audit_logger.log_security_event(
                    "HIGH_PRIORITY_ORACLE_NOTIFICATION",
                    {"message": message[:100], "priority": priority}
                )
            
            return True
            
        except Exception as e:
            logging.error(f"❌ Notification failed: {e}")
            return False
    
    async def send_alert(self, alert_type: str, details: Dict[str, Any]) -> bool:
        """Send alert notification"""
        try:
            message = f"🚨 ORACLE ALERT [{alert_type}]: {details}"
            logging.warning(message)
            
            audit_logger.log_security_event(
                "ORACLE_ALERT_SENT",
                {"alert_type": alert_type, "details": details},
                severity="WARNING"
            )
            
            return True
            
        except Exception as e:
            logging.error(f"❌ Alert failed: {e}")
            return False


class EnhancedOracleService:
    """
    Enhanced Oracle service with modern architecture
    """
    
    def __init__(self, container: ServiceContainer):
        self.config = get_secure_config()
        
        # Inject dependencies
        self.data_source = container.resolve(EnhancedTokenUnlocksDataSource)
        self.notification_service = container.resolve(EnhancedOracleNotificationService)
        
        # Service metrics
        self.jobs_completed = 0
        self.total_events_processed = 0
        self.last_job_time: Optional[datetime] = None
        
        logging.info("🔮 Enhanced Oracle service initialized")
    
    async def run_data_collection_job(self) -> Dict[str, Any]:
        """Run the main data collection job"""
        job_start_time = datetime.now(timezone.utc)
        job_results = {
            'start_time': job_start_time.isoformat(),
            'events_fetched': 0,
            'events_stored': 0,
            'events_published': 0,
            'success': False,
            'errors': []
        }
        
        try:
            logging.info("🚀 Starting Oracle data collection job...")
            
            # Step 1: Fetch unlock events
            await self.notification_service.send_notification(
                "Oracle data collection job started", priority=1
            )
            
            events = await self.data_source.fetch_unlock_events()
            job_results['events_fetched'] = len(events)
            
            if not events:
                logging.warning("⚠️ No events fetched, ending job")
                await self.notification_service.send_notification(
                    "No unlock events found in data sources", priority=2
                )
                return job_results
            
            # Step 2: Validate and store events
            valid_events = []
            for event in events:
                if await self.data_source.validate_data(event):
                    valid_events.append(event)
                else:
                    job_results['errors'].append(f"Invalid event data: {event.get('token_symbol', 'unknown')}")
            
            logging.info(f"✅ Validated {len(valid_events)}/{len(events)} events")
            
            # Step 3: Store in database (mock for now)
            stored_count = await self._store_events(valid_events)
            job_results['events_stored'] = stored_count
            
            # Step 4: Publish upcoming events
            published_count = await self._publish_upcoming_events()
            job_results['events_published'] = published_count
            
            # Update metrics
            self.jobs_completed += 1
            self.total_events_processed += len(valid_events)
            self.last_job_time = job_start_time
            
            job_results['success'] = True
            
            # Send success notification
            await self.notification_service.send_notification(
                f"Oracle job completed: {stored_count} events stored, {published_count} published",
                priority=2
            )
            
            logging.info(f"✅ Oracle job completed successfully in {(datetime.now(timezone.utc) - job_start_time).total_seconds():.2f}s")
            
        except Exception as e:
            job_results['errors'].append(str(e))
            
            log_error_with_context(
                e,
                context={
                    'operation': 'oracle_data_collection',
                    'job_start_time': job_start_time.isoformat(),
                    'events_fetched': job_results['events_fetched']
                }
            )
            
            await self.notification_service.send_alert(
                "JOB_FAILED",
                {"error": str(e), "job_time": job_start_time.isoformat()}
            )
            
            raise
        
        finally:
            job_results['end_time'] = datetime.now(timezone.utc).isoformat()
            job_results['duration_seconds'] = (
                datetime.now(timezone.utc) - job_start_time
            ).total_seconds()
        
        return job_results
    
    async def _store_events(self, events: List[Dict[str, Any]]) -> int:
        """Store events in database (mock implementation)"""
        # In a real implementation, this would use the async database pool
        logging.info(f"💾 Storing {len(events)} events in database...")
        
        # Simulate database storage
        await asyncio.sleep(0.1)
        
        return len(events)
    
    async def _publish_upcoming_events(self) -> int:
        """Publish upcoming events to Redis (mock implementation)"""
        # In a real implementation, this would publish to Redis pub/sub
        logging.info("📤 Publishing upcoming events to Redis...")
        
        # Simulate Redis publishing
        await asyncio.sleep(0.05)
        
        return 2  # Mock published count
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get service metrics"""
        return {
            'jobs_completed': self.jobs_completed,
            'total_events_processed': self.total_events_processed,
            'last_job_time': self.last_job_time.isoformat() if self.last_job_time else None,
            'data_source_metrics': self.data_source.get_metrics()
        }


async def main():
    """Main entry point with dependency injection setup"""
    logging.info("🔮 Starting Enhanced Oracle - Data Collection Service")
    
    try:
        # Setup dependency injection container
        container = get_container()
        
        # Register services
        container.register_singleton(EnhancedTokenUnlocksDataSource)
        container.register_singleton(EnhancedOracleNotificationService)
        
        # Initialize async database (if needed)
        # await initialize_async_database(config.database.url)
        
        # Create and run the Oracle service
        oracle = EnhancedOracleService(container)
        
        # Run the data collection job
        results = await oracle.run_data_collection_job()
        
        # Print results
        logging.info(f"📊 Job Results: {results}")
        
        # Print metrics
        metrics = oracle.get_metrics()
        logging.info(f"📈 Service Metrics: {metrics}")
        
    except KeyboardInterrupt:
        logging.info("🛑 Received shutdown signal")
    except Exception as e:
        logging.error(f"❌ Fatal error in Oracle service: {e}")
        raise
    finally:
        # Cleanup
        # await cleanup_async_database()
        pass


if __name__ == "__main__":
    asyncio.run(main())
