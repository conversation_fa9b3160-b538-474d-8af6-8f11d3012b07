"""
Enhanced Secure Configuration Management - 2025 Security Best Practices
======================================================================

Advanced configuration management with security features:
- Encrypted secret management
- Input validation and sanitization
- Security audit logging
- Environment-based configuration
- Type-safe configuration classes
- Configuration validation and error reporting
"""

import os
import logging
from enum import Enum
from typing import Optional, Dict, Any, List, Union
from dataclasses import dataclass, field
from decimal import Decimal
from datetime import datetime, timezone

from .security import SecretManager, InputValidator, SecurityError, audit_logger
from .constants import TradingConstants, SystemConstants, ValidationConstants


class Environment(Enum):
    """Deployment environment types"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


class ConfigurationError(Exception):
    """Configuration-related error"""
    pass


@dataclass
class SecureDatabaseConfig:
    """Secure database configuration with validation"""
    url: str = field(default="")
    pool_size: int = field(default=10)
    max_overflow: int = field(default=20)
    pool_timeout: int = field(default=30)
    ssl_required: bool = field(default=True)
    connection_timeout: int = field(default=30)
    
    def __post_init__(self):
        # Get encrypted database URL
        secret_manager = SecretManager()
        self.url = secret_manager.get_secret("DATABASE_URL") or self.url
        
        if not self.url:
            raise ConfigurationError("DATABASE_URL is required")
        
        # Validate URL format (basic check)
        if not self.url.startswith(('postgresql://', 'postgres://')):
            raise ConfigurationError("Invalid database URL format")
        
        # Validate numeric parameters
        if self.pool_size < 1 or self.pool_size > 100:
            raise ConfigurationError("Pool size must be between 1 and 100")
        
        if self.max_overflow < 0 or self.max_overflow > 200:
            raise ConfigurationError("Max overflow must be between 0 and 200")
        
        # Log configuration (without sensitive data)
        audit_logger.log_security_event(
            "DATABASE_CONFIG_LOADED",
            {
                "pool_size": self.pool_size,
                "ssl_required": self.ssl_required,
                "has_url": bool(self.url)
            }
        )


@dataclass
class SecureRedisConfig:
    """Secure Redis configuration with validation"""
    url: str = field(default="")
    max_connections: int = field(default=50)
    socket_timeout: int = field(default=30)
    socket_connect_timeout: int = field(default=30)
    ssl_required: bool = field(default=False)
    
    def __post_init__(self):
        # Get encrypted Redis URL
        secret_manager = SecretManager()
        self.url = secret_manager.get_secret("REDIS_URL") or self.url
        
        if not self.url:
            raise ConfigurationError("REDIS_URL is required")
        
        # Validate URL format
        if not self.url.startswith(('redis://', 'rediss://')):
            raise ConfigurationError("Invalid Redis URL format")
        
        # Validate parameters
        if self.max_connections < 1 or self.max_connections > 1000:
            raise ConfigurationError("Max connections must be between 1 and 1000")
        
        audit_logger.log_security_event(
            "REDIS_CONFIG_LOADED",
            {
                "max_connections": self.max_connections,
                "ssl_required": self.ssl_required,
                "has_url": bool(self.url)
            }
        )


@dataclass
class SecureAPIConfig:
    """Secure API configuration with validation"""
    infura_api_key: str = field(default="")
    coingecko_api_key: str = field(default="")
    telegram_bot_token: str = field(default="")
    telegram_chat_id: str = field(default="")
    
    # Rate limiting
    rate_limit_per_second: int = field(default=100)
    rate_limit_burst: int = field(default=200)
    
    # Security settings
    api_timeout_seconds: int = field(default=30)
    max_retries: int = field(default=3)
    require_api_key: bool = field(default=True)
    
    def __post_init__(self):
        secret_manager = SecretManager()
        
        # Load encrypted API keys
        self.infura_api_key = secret_manager.get_secret("INFURA_API_KEY") or self.infura_api_key
        self.coingecko_api_key = secret_manager.get_secret("COINGECKO_API_KEY") or self.coingecko_api_key
        self.telegram_bot_token = secret_manager.get_secret("TELEGRAM_BOT_TOKEN") or self.telegram_bot_token
        self.telegram_chat_id = secret_manager.get_secret("TELEGRAM_CHAT_ID") or self.telegram_chat_id
        
        # Validate API keys format (basic validation)
        if self.infura_api_key and len(self.infura_api_key) < 20:
            raise ConfigurationError("Invalid Infura API key format")
        
        if self.telegram_bot_token and not self.telegram_bot_token.count(':') == 1:
            raise ConfigurationError("Invalid Telegram bot token format")
        
        # Validate rate limiting
        if self.rate_limit_per_second < 1 or self.rate_limit_per_second > 10000:
            raise ConfigurationError("Rate limit must be between 1 and 10000")
        
        audit_logger.log_security_event(
            "API_CONFIG_LOADED",
            {
                "has_infura_key": bool(self.infura_api_key),
                "has_coingecko_key": bool(self.coingecko_api_key),
                "has_telegram_token": bool(self.telegram_bot_token),
                "rate_limit": self.rate_limit_per_second,
                "require_api_key": self.require_api_key
            }
        )


@dataclass
class SecureTradingConfig:
    """Secure trading configuration with validation"""
    paper_trading_mode: bool = field(default=True)
    stop_loss_pct: Decimal = field(default=TradingConstants.DEFAULT_STOP_LOSS_PCT)
    take_profit_pct: Decimal = field(default=TradingConstants.DEFAULT_TAKE_PROFIT_PCT)
    position_size_usd: Decimal = field(default=TradingConstants.DEFAULT_POSITION_SIZE_USD)
    max_positions: int = field(default=10)
    pressure_score_threshold: float = field(default=0.75)
    
    # Risk management
    max_daily_loss_usd: Decimal = field(default=Decimal("5000"))
    max_position_size_usd: Decimal = field(default=TradingConstants.MAX_POSITION_SIZE_USD)
    monitoring_interval_seconds: int = field(default=60)
    
    # Private key management
    private_key: str = field(default="")
    wallet_address: str = field(default="")
    
    def __post_init__(self):
        secret_manager = SecretManager()
        
        # Load encrypted private key
        self.private_key = secret_manager.get_secret("PRIVATE_KEY") or self.private_key
        self.wallet_address = secret_manager.get_secret("WALLET_ADDRESS") or self.wallet_address
        
        # Validate trading parameters
        if self.stop_loss_pct <= 0 or self.stop_loss_pct >= 1:
            raise ConfigurationError("Stop loss percentage must be between 0 and 1")
        
        if self.take_profit_pct <= 0 or self.take_profit_pct >= 1:
            raise ConfigurationError("Take profit percentage must be between 0 and 1")
        
        if self.position_size_usd <= 0:
            raise ConfigurationError("Position size must be positive")
        
        if self.max_positions < 1 or self.max_positions > 100:
            raise ConfigurationError("Max positions must be between 1 and 100")
        
        # Validate wallet address if provided
        if self.wallet_address:
            try:
                self.wallet_address = InputValidator.validate_ethereum_address(self.wallet_address)
            except SecurityError as e:
                raise ConfigurationError(f"Invalid wallet address: {e}")
        
        # Validate private key format (basic check)
        if self.private_key and not self.private_key.startswith('0x'):
            if len(self.private_key) != 64:  # 32 bytes = 64 hex chars
                raise ConfigurationError("Invalid private key format")
        
        audit_logger.log_security_event(
            "TRADING_CONFIG_LOADED",
            {
                "paper_trading_mode": self.paper_trading_mode,
                "stop_loss_pct": float(self.stop_loss_pct),
                "take_profit_pct": float(self.take_profit_pct),
                "max_positions": self.max_positions,
                "has_private_key": bool(self.private_key),
                "has_wallet_address": bool(self.wallet_address)
            }
        )


@dataclass
class SecureSystemConfig:
    """Secure system configuration"""
    environment: Environment = field(default=Environment.DEVELOPMENT)
    debug: bool = field(default=False)
    log_level: str = field(default="INFO")
    
    # Security settings
    enable_audit_logging: bool = field(default=True)
    enable_rate_limiting: bool = field(default=True)
    enable_ip_blocking: bool = field(default=True)
    session_timeout_minutes: int = field(default=60)
    
    # Performance settings
    max_concurrent_requests: int = field(default=100)
    request_timeout_seconds: int = field(default=30)
    
    def __post_init__(self):
        # Load environment
        env_str = os.getenv("ENVIRONMENT", "development").lower()
        try:
            self.environment = Environment(env_str)
        except ValueError:
            raise ConfigurationError(f"Invalid environment: {env_str}")
        
        # Load debug setting
        self.debug = os.getenv("DEBUG", "false").lower() == "true"
        
        # Validate log level
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        self.log_level = os.getenv("LOG_LEVEL", "INFO").upper()
        if self.log_level not in valid_levels:
            raise ConfigurationError(f"Invalid log level: {self.log_level}")
        
        # Production security checks
        if self.environment == Environment.PRODUCTION:
            if self.debug:
                raise ConfigurationError("Debug mode must be disabled in production")
            
            if not self.enable_audit_logging:
                raise ConfigurationError("Audit logging must be enabled in production")
        
        audit_logger.log_security_event(
            "SYSTEM_CONFIG_LOADED",
            {
                "environment": self.environment.value,
                "debug": self.debug,
                "log_level": self.log_level,
                "audit_logging": self.enable_audit_logging
            }
        )


class SecureConfigManager:
    """
    Centralized secure configuration manager
    """
    
    def __init__(self):
        self.database = SecureDatabaseConfig()
        self.redis = SecureRedisConfig()
        self.api = SecureAPIConfig()
        self.trading = SecureTradingConfig()
        self.system = SecureSystemConfig()
        
        # Validate overall configuration
        self._validate_configuration()
        
        logging.info("✅ Secure configuration loaded successfully")
    
    def _validate_configuration(self):
        """Validate overall configuration consistency"""
        issues = []
        
        # Production-specific validations
        if self.system.environment == Environment.PRODUCTION:
            if self.trading.paper_trading_mode:
                issues.append("Paper trading should be disabled in production")
            
            if not self.api.infura_api_key:
                issues.append("Infura API key is required for production")
            
            if not self.trading.private_key:
                issues.append("Private key is required for production")
        
        # Trading configuration consistency
        if self.trading.stop_loss_pct >= self.trading.take_profit_pct:
            issues.append("Stop loss should be less than take profit for short positions")
        
        if self.trading.position_size_usd > self.trading.max_position_size_usd:
            issues.append("Position size exceeds maximum allowed")
        
        if issues:
            error_msg = "Configuration validation failed: " + "; ".join(issues)
            audit_logger.log_security_event(
                "CONFIG_VALIDATION_FAILED",
                {"issues": issues},
                severity="ERROR"
            )
            raise ConfigurationError(error_msg)
        
        audit_logger.log_security_event(
            "CONFIG_VALIDATION_PASSED",
            {"environment": self.system.environment.value}
        )
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get configuration summary (without sensitive data)"""
        return {
            "environment": self.system.environment.value,
            "paper_trading": self.trading.paper_trading_mode,
            "debug": self.system.debug,
            "has_database_url": bool(self.database.url),
            "has_redis_url": bool(self.redis.url),
            "has_infura_key": bool(self.api.infura_api_key),
            "has_telegram_token": bool(self.api.telegram_bot_token),
            "has_private_key": bool(self.trading.private_key),
            "max_positions": self.trading.max_positions,
            "stop_loss_pct": float(self.trading.stop_loss_pct),
            "take_profit_pct": float(self.trading.take_profit_pct)
        }


# Global configuration instance
secure_config: Optional[SecureConfigManager] = None


def get_secure_config() -> SecureConfigManager:
    """Get the global secure configuration instance"""
    global secure_config
    
    if secure_config is None:
        secure_config = SecureConfigManager()
    
    return secure_config


def reload_secure_config():
    """Reload configuration (useful for hot-reloading)"""
    global secure_config
    
    audit_logger.log_security_event(
        "CONFIG_RELOAD_REQUESTED",
        {"timestamp": datetime.now(timezone.utc).isoformat()}
    )
    
    secure_config = SecureConfigManager()
    
    audit_logger.log_security_event(
        "CONFIG_RELOAD_COMPLETED",
        {"timestamp": datetime.now(timezone.utc).isoformat()}
    )


# Export public interface
__all__ = [
    'Environment',
    'ConfigurationError',
    'SecureDatabaseConfig',
    'SecureRedisConfig', 
    'SecureAPIConfig',
    'SecureTradingConfig',
    'SecureSystemConfig',
    'SecureConfigManager',
    'get_secure_config',
    'reload_secure_config'
]
